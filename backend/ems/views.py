from rest_framework import viewsets, status, filters
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User
from .permissions import (
    RoleBasedPermission,
    EmployeePermission,
    HRManagerPermission,
    AdminPermission,
    EmployeeDataPermission,
    IsOwnerOrReadOnly
)
from django.db.models import Q
from django.utils import timezone
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from django.core.cache import cache
from .models import (
    Department, Employee, Activity, Role, UserProfile, LeaveType, LeaveRequest,
    Attendance, Project, Task, Budget, Expense, AssetCategory, Asset, Supplier,
    PurchaseOrder, Announcement, Message, Document, Meeting, Customer,
    ProductCategory, Product, Report, SalesOrder, Workflow
)
from .serializers import (
    DepartmentSerializer, EmployeeSerializer, ActivitySerializer, DashboardStatsSerializer,
    UserSerializer, RoleSerializer, UserProfileSerializer, LeaveTypeSerializer,
    LeaveRequestSerializer, EmployeeLeaveSerializer, AttendanceSerializer, ProjectSerializer,
    TaskSerializer, BudgetSerializer, ExpenseSerializer, AssetCategorySerializer, AssetSerializer,
    SupplierSerializer, PurchaseOrderSerializer, AnnouncementSerializer,
    MessageSerializer, DocumentSerializer, MeetingSerializer, PersonalCalendarSerializer,
    CustomerSerializer, ProductCategorySerializer, ProductSerializer, ReportSerializer,
    SalesOrderSerializer, WorkflowSerializer
)

class DepartmentViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    # Note: 'employees' should be 'employee_set' for reverse relationship
    queryset = Department.objects.select_related('manager__user').prefetch_related('employee_set').all()
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated]

class EmployeeViewSet(viewsets.ModelViewSet):
    queryset = Employee.objects.select_related('user', 'department').all()
    serializer_class = EmployeeSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        from django.db.models import Q
        queryset = super().get_queryset()

        # Department filter
        department = self.request.query_params.get('department', None)
        if department is not None:
            # Try to filter by department ID first, then by name
            try:
                # If it's a number, filter by ID
                dept_id = int(department)
                queryset = queryset.filter(department_id=dept_id)
            except ValueError:
                # If it's not a number, filter by department name
                queryset = queryset.filter(department__name__icontains=department)

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(first_name_ar__icontains=search) |
                Q(last_name_ar__icontains=search) |
                Q(user__email__icontains=search) |
                Q(employee_id__icontains=search) |
                Q(position__icontains=search) |
                Q(position_ar__icontains=search) |
                Q(department__name__icontains=search) |
                Q(department__name_ar__icontains=search) |
                Q(phone__icontains=search)
            )

        # Employment status filter
        employment_status = self.request.query_params.get('employment_status', None)
        if employment_status is not None:
            queryset = queryset.filter(employment_status=employment_status)

        # Active status filter
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('user__first_name', 'user__last_name')

    @action(detail=False, methods=['get'], url_path='dropdown-list')
    def dropdown_list(self, request):
        """Get simplified employee list for dropdowns"""
        try:
            employees = Employee.objects.select_related('user').filter(is_active=True)

            # Apply department filter if provided
            department = request.query_params.get('department')
            if department:
                employees = employees.filter(department_id=department)

            # Create simplified data for dropdowns
            employee_list = []
            for emp in employees:
                employee_list.append({
                    'id': emp.id,
                    'employee_id': emp.employee_id,
                    'name': emp.user.get_full_name(),
                    'name_ar': f"{emp.first_name_ar} {emp.last_name_ar}" if emp.first_name_ar and emp.last_name_ar else emp.user.get_full_name(),
                    'department': emp.department.name if emp.department else None,
                    'position': emp.position,
                    'email': emp.user.email
                })

            return Response(employee_list, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch employee list: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ActivityViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Activity.objects.select_related('user').all()
    serializer_class = ActivitySerializer
    permission_classes = [IsAuthenticated]

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def employee_export(request):
    """Export employees data in various formats"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # Get employees queryset
    queryset = Employee.objects.select_related('user', 'department').all()

    # Apply department filter if provided
    department = request.GET.get('department')
    if department:
        queryset = queryset.filter(department=department)

    # Prepare data
    headers = [
        'Employee ID', 'First Name', 'Last Name', 'Email', 'Position',
        'Position (Arabic)', 'Department', 'Department (Arabic)',
        'Phone', 'Gender', 'Hire Date', 'Salary', 'Status'
    ]

    data = []
    for employee in queryset:
        data.append([
            employee.employee_id,
            employee.user.first_name,
            employee.user.last_name,
            employee.user.email,
            employee.position,
            employee.position_ar,
            employee.department.name if employee.department else '',
            employee.department.name_ar if employee.department else '',
            employee.phone,
            employee.get_gender_display(),
            employee.hire_date,
            employee.salary,
            'Active' if employee.is_active else 'Inactive'
        ])

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="employees.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

    elif export_format == 'excel':
        try:
            import openpyxl
            from openpyxl import Workbook
            from io import BytesIO

            wb = Workbook()
            ws = wb.active
            ws.title = "Employees"

            # Add headers
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # Add data
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)

            # Save to BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="employees.xlsx"'
            return response

        except ImportError:
            # Fallback to CSV if openpyxl is not available
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="employees.csv"'

            writer = csv.writer(response)
            writer.writerow(headers)
            writer.writerows(data)

            return response

    else:
        # Default to CSV for unsupported formats
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="employees.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def department_export(request):
    """Export departments data in various formats"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # CRITICAL FIX: Get departments queryset with optimized queries
    # Note: 'employees' should be 'employee_set' for reverse relationship
    queryset = Department.objects.select_related('manager__user').prefetch_related('employee_set').all()

    # Apply filters if provided
    is_active = request.GET.get('is_active')
    if is_active:
        queryset = queryset.filter(is_active=is_active.lower() == 'true')

    # Prepare data
    headers = [
        'Department ID', 'Name', 'Name (Arabic)', 'Description', 'Description (Arabic)',
        'Manager', 'Manager (Arabic)', 'Location', 'Phone', 'Email', 'Budget',
        'Employee Count', 'Status', 'Created Date'
    ]

    data = []
    for department in queryset:
        # Get manager information
        manager_name = ''
        manager_name_ar = ''
        if department.manager:
            manager_name = department.manager.user.get_full_name()
            manager_name_ar = f"{department.manager.first_name_ar or ''} {department.manager.last_name_ar or ''}".strip()

        data.append([
            department.id,
            department.name,
            department.name_ar or '',
            department.description or '',
            department.description_ar or '',
            manager_name,
            manager_name_ar,
            department.location or '',
            department.phone or '',
            department.email or '',
            department.budget_amount or 0,
            department.employees.count() if hasattr(department, 'employees') else 0,
            'Active' if department.is_active else 'Inactive',
            department.created_at.strftime('%Y-%m-%d') if department.created_at else ''
        ])

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="departments.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

    elif export_format == 'excel':
        try:
            import openpyxl
            from openpyxl import Workbook
            from io import BytesIO

            wb = Workbook()
            ws = wb.active
            ws.title = "Departments"

            # Add headers
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # Add data
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)

            # Save to BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="departments.xlsx"'
            return response

        except ImportError:
            # Fallback to CSV if openpyxl is not available
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="departments.csv"'

            writer = csv.writer(response)
            writer.writerow(headers)
            writer.writerows(data)

            return response

    else:
        # Default to CSV for unsupported formats
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="departments.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def attendance_export(request):
    """Export attendance data in various formats"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # Get attendance queryset
    queryset = Attendance.objects.select_related('employee__user').all()

    # Apply filters if provided
    employee = request.GET.get('employee')
    if employee:
        queryset = queryset.filter(employee=employee)

    date_from = request.GET.get('date_from')
    if date_from:
        queryset = queryset.filter(date__gte=date_from)

    date_to = request.GET.get('date_to')
    if date_to:
        queryset = queryset.filter(date__lte=date_to)

    # Prepare data
    headers = [
        'Employee ID', 'Employee Name', 'Date', 'Check In', 'Check Out',
        'Break Start', 'Break End', 'Total Hours', 'Overtime Hours',
        'Present', 'Late', 'Notes'
    ]

    data = []
    for attendance in queryset:
        data.append([
            attendance.employee.employee_id,
            attendance.employee.user.get_full_name(),
            attendance.date.strftime('%Y-%m-%d'),
            attendance.check_in.strftime('%H:%M:%S') if attendance.check_in else '',
            attendance.check_out.strftime('%H:%M:%S') if attendance.check_out else '',
            attendance.break_start.strftime('%H:%M:%S') if attendance.break_start else '',
            attendance.break_end.strftime('%H:%M:%S') if attendance.break_end else '',
            str(attendance.total_hours) if attendance.total_hours else '',
            str(attendance.overtime_hours) if attendance.overtime_hours else '0',
            'Yes' if attendance.is_present else 'No',
            'Yes' if attendance.is_late else 'No',
            attendance.notes or ''
        ])

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

    elif export_format == 'excel':
        try:
            import openpyxl
            from openpyxl import Workbook
            from io import BytesIO

            wb = Workbook()
            ws = wb.active
            ws.title = "Attendance"

            # Add headers
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # Add data
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)

            # Save to BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="attendance.xlsx"'
            return response

        except ImportError:
            # Fallback to CSV if openpyxl is not available
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

            writer = csv.writer(response)
            writer.writerow(headers)
            writer.writerows(data)

            return response

    else:
        # Default to CSV for unsupported formats
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def test_export(request):
    """Test export function - now returns attendance CSV"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # Get attendance queryset
    queryset = Attendance.objects.select_related('employee__user').all()[:10]  # Limit to 10 for testing

    # Prepare data
    headers = [
        'Employee ID', 'Employee Name', 'Date', 'Check In', 'Check Out',
        'Total Hours', 'Present', 'Late'
    ]

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)

        for attendance in queryset:
            writer.writerow([
                attendance.employee.employee_id,
                attendance.employee.user.get_full_name(),
                attendance.date.strftime('%Y-%m-%d'),
                attendance.check_in.strftime('%H:%M:%S') if attendance.check_in else '',
                attendance.check_out.strftime('%H:%M:%S') if attendance.check_out else '',
                str(attendance.total_hours) if attendance.total_hours else '',
                'Yes' if attendance.is_present else 'No',
                'Yes' if attendance.is_late else 'No',
            ])

        return response
    else:
        response = HttpResponse("Attendance export working! Format: " + export_format, content_type='text/plain')
        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def simple_attendance_export(request):
    """Simple attendance export function"""
    from django.http import HttpResponse
    response = HttpResponse("Simple attendance export working!", content_type='text/plain')
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@cache_page(60 * 5)  # Cache for 5 minutes
def dashboard_stats(request):
    """
    Get dashboard statistics matching frontend interface
    """
    from django.db.models import Sum, Count
    import psutil

    try:
        total_employees = Employee.objects.filter(is_active=True).count()
        total_departments = Department.objects.count()
        active_projects = Project.objects.filter(status='IN_PROGRESS').count()
        pending_tasks = Task.objects.filter(status='TODO').count()
        pending_leave_requests = LeaveRequest.objects.filter(status='PENDING').count()

        # Calculate monthly expenses (current month)
        from datetime import datetime, timedelta
        current_month = datetime.now().replace(day=1)
        monthly_expenses = Expense.objects.filter(
            expense_date__gte=current_month,
            status='APPROVED'
        ).aggregate(total=Sum('amount'))['total'] or 0

        # System health metrics
        try:
            cpu_usage = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            system_health = {
                'cpu_usage': cpu_usage,
                'memory_usage': memory.percent,
                'disk_usage': (disk.used / disk.total) * 100
            }
        except:
            # Fallback if psutil is not available - return empty states instead of fake data
            system_health = {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0
            }

        data = {
            'total_employees': total_employees,
            'active_employees': total_employees,  # Add active_employees for compatibility
            'total_departments': total_departments,
            'active_projects': active_projects,
            'pending_tasks': pending_tasks,
            'pending_leave_requests': pending_leave_requests,
            'monthly_expenses': float(monthly_expenses),
            'system_health': system_health
        }

        return Response(data)

    except Exception as e:
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Dashboard stats error: {str(e)}")

        # Return fallback data instead of error
        total_emp_count = Employee.objects.count()
        fallback_data = {
            'total_employees': total_emp_count,
            'active_employees': total_emp_count,  # Add active_employees for compatibility
            'total_departments': Department.objects.count(),
            'active_projects': 0,
            'pending_tasks': 0,
            'pending_leave_requests': 0,
            'monthly_expenses': 0.0,
            'system_health': {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0
            }
        }

        return Response(fallback_data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def superadmin_system_stats(request):
    """
    Get real system administration statistics for SUPERADMIN
    """
    import psutil
    import platform
    import os
    from django.db import connection
    from django.contrib.auth.models import User
    from django.contrib.sessions.models import Session
    from datetime import datetime, timedelta

    # Check if user has SUPERADMIN role
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        if user_profile.role.name != 'SUPERADMIN':
            return Response({
                'error': 'Access denied. SUPERADMIN role required.'
            }, status=status.HTTP_403_FORBIDDEN)
    except UserProfile.DoesNotExist:
        return Response({
            'error': 'User profile not found'
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        # Real System Metrics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time

        # Network statistics
        network = psutil.net_io_counters()

        # Database statistics
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM django_session WHERE expire_date > %s", [datetime.now()])
            active_sessions = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM auth_user WHERE is_active = true")
            total_users = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM auth_user WHERE last_login > %s", [datetime.now() - timedelta(hours=24)])
            active_users_24h = cursor.fetchone()[0]

            # Get database size (PostgreSQL specific, fallback for other DBs)
            try:
                cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()))")
                db_size = cursor.fetchone()[0]
            except:
                db_size = "N/A"

        # Security metrics
        failed_logins_24h = 0  # This would need to be tracked in a separate model
        blocked_ips = 0  # This would need to be tracked in a separate model

        # System information
        system_info = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor() or 'Unknown',
            'hostname': platform.node()
        }

        # Format uptime
        uptime_str = f"{uptime.days} days, {uptime.seconds // 3600} hours"

        data = {
            'system_health': {
                'status': 'healthy' if cpu_usage < 80 and memory.percent < 85 else 'warning' if cpu_usage < 95 and memory.percent < 95 else 'critical',
                'uptime': uptime_str,
                'boot_time': boot_time.isoformat()
            },
            'server_metrics': {
                'cpu_usage': round(cpu_usage, 1),
                'memory_usage': round(memory.percent, 1),
                'memory_total': round(memory.total / (1024**3), 2),  # GB
                'memory_available': round(memory.available / (1024**3), 2),  # GB
                'disk_usage': round((disk.used / disk.total) * 100, 1),
                'disk_total': round(disk.total / (1024**3), 2),  # GB
                'disk_free': round(disk.free / (1024**3), 2),  # GB
                'network_sent': round(network.bytes_sent / (1024**2), 2),  # MB
                'network_recv': round(network.bytes_recv / (1024**2), 2)   # MB
            },
            'database_stats': {
                'size': db_size,
                'active_connections': active_sessions,
                'total_tables': len(connection.introspection.table_names()),
                'status': 'online'
            },
            'user_stats': {
                'total_system_users': total_users,
                'active_sessions': active_sessions,
                'active_users_24h': active_users_24h,
                'superadmin_users': UserProfile.objects.filter(role__name='SUPERADMIN').count(),
                'admin_users': UserProfile.objects.filter(role__name='ADMIN').count()
            },
            'security_stats': {
                'security_score': calculate_security_score(),
                'active_threats': get_active_threats_count(),
                'blocked_attacks': get_blocked_attacks_count(),
                'failed_logins_24h': failed_logins_24h,
                'blocked_ips': blocked_ips
            },
            'system_info': system_info,
            'last_updated': datetime.now().isoformat()
        }

        return Response(data)

    except Exception as e:
        return Response({
            'error': 'Failed to fetch system stats',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """
    Get current user profile
    """
    try:
        employee = Employee.objects.select_related('user', 'department').get(user=request.user)
        serializer = EmployeeSerializer(employee)
        return Response(serializer.data)
    except Employee.DoesNotExist:
        user_serializer = UserSerializer(request.user)
        return Response(user_serializer.data)

# HR Management ViewSets
class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all().order_by('name')
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]

class UserProfileViewSet(viewsets.ModelViewSet):
    queryset = UserProfile.objects.select_related('user', 'role').all().order_by('user__first_name', 'user__last_name')
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]

class LeaveTypeViewSet(viewsets.ModelViewSet):
    queryset = LeaveType.objects.filter(is_active=True)
    serializer_class = LeaveTypeSerializer
    permission_classes = [IsAuthenticated]

class LeaveRequestViewSet(viewsets.ModelViewSet):
    queryset = LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user').all()
    serializer_class = LeaveRequestSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'], url_path='my-requests', permission_classes=[IsAuthenticated])
    def my_requests(self, request):
        """Get current user's leave requests"""
        try:
            employee = Employee.objects.get(user=request.user)
            queryset = self.queryset.filter(employee=employee)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in my_requests for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

class AttendanceViewSet(viewsets.ModelViewSet):
    queryset = Attendance.objects.select_related('employee__user').all()
    serializer_class = AttendanceSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'], url_path='my-attendance', permission_classes=[IsAuthenticated])
    def my_attendance(self, request):
        """Get current user's attendance records"""
        try:
            employee = Employee.objects.get(user=request.user)
            queryset = self.queryset.filter(employee=employee)

            # Filter by date range if provided
            date_from = request.query_params.get('date_from')
            date_to = request.query_params.get('date_to')

            if date_from:
                queryset = queryset.filter(date__gte=date_from)
            if date_to:
                queryset = queryset.filter(date__lte=date_to)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in my_attendance for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

# Project Management ViewSets
class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.select_related('project_manager__user', 'department').prefetch_related('team_members', 'tasks').all()
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated, EmployeePermission, EmployeeDataPermission]

    def get_queryset(self):
        """Filter projects based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            # FIXED: Use employee profile instead of userprofile for project filtering
            employee_profile = user.employee
            user_profile = user.userprofile
            user_role = user_profile.role

            # Super admin and admin see all projects
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see projects they're assigned to
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(team_members=employee_profile)

            # Other roles see projects in their department
            if employee_profile.department:
                return queryset.filter(department=employee_profile.department)

            return queryset
        except:
            return queryset.none()

    @action(detail=True, methods=['patch'], url_path='update-progress', permission_classes=[IsAuthenticated])
    def update_progress(self, request, pk=None):
        """Update project progress - employees can update progress for projects they're assigned to"""
        try:
            project = self.get_object()
            user = request.user

            # Check if user is assigned to this project
            try:
                employee_profile = user.employee
                if not project.team_members.filter(id=employee_profile.id).exists():
                    return Response(
                        {'error': 'You are not assigned to this project'},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except:
                return Response(
                    {'error': 'Employee profile not found'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Get progress data from request
            progress_percentage = request.data.get('progress_percentage')
            progress_note = request.data.get('progress_note', '')

            # Validate progress percentage
            if progress_percentage is None:
                return Response(
                    {'error': 'progress_percentage is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                progress_percentage = float(progress_percentage)
                if progress_percentage < 0 or progress_percentage > 100:
                    return Response(
                        {'error': 'progress_percentage must be between 0 and 100'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except (ValueError, TypeError):
                return Response(
                    {'error': 'progress_percentage must be a valid number'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Update project progress
            project.progress_percentage = progress_percentage
            project.save()

            # TODO: Create a ProjectProgressUpdate model to track progress history
            # For now, we'll just update the project directly

            # Return updated project data
            serializer = self.get_serializer(project)
            return Response({
                'message': 'Progress updated successfully',
                'project': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to update progress: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TaskViewSet(viewsets.ModelViewSet):
    queryset = Task.objects.select_related('project', 'assigned_to__user', 'created_by__user').all()
    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated, EmployeePermission, EmployeeDataPermission]

    def get_queryset(self):
        """Filter tasks based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            user_profile = user.userprofile
            user_role = user_profile.role

            # Super admin and admin see all tasks
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see tasks assigned to them
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(assigned_to=user_profile)

            # HR managers see all tasks
            if user_role and user_role.name == 'HR_MANAGER':
                return queryset

            # Other roles see tasks in their department
            if user_profile.department:
                return queryset.filter(
                    Q(assigned_to__department=user_profile.department) |
                    Q(project__department=user_profile.department)
                )

            return queryset
        except:
            return queryset.none()

    @action(detail=False, methods=['get'], url_path='my-tasks', permission_classes=[IsAuthenticated])
    def my_tasks(self, request):
        """Get current user's assigned tasks"""
        try:
            # Try to get Employee record first
            employee = Employee.objects.get(user=request.user)
            queryset = self.queryset.filter(assigned_to=employee)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            # If no Employee record, return empty list
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            # Log any other errors and return empty list
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in my_tasks for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

# Financial Management ViewSets
class BudgetViewSet(viewsets.ModelViewSet):
    queryset = Budget.objects.select_related('department', 'project', 'created_by__user').all()
    serializer_class = BudgetSerializer
    permission_classes = [IsAuthenticated]

class ExpenseViewSet(viewsets.ModelViewSet):
    queryset = Expense.objects.select_related('employee__user', 'budget', 'project', 'approved_by__user').all()
    serializer_class = ExpenseSerializer
    permission_classes = [IsAuthenticated]

# Asset Management ViewSets
class AssetCategoryViewSet(viewsets.ModelViewSet):
    queryset = AssetCategory.objects.all()
    serializer_class = AssetCategorySerializer
    permission_classes = [IsAuthenticated]

class AssetViewSet(viewsets.ModelViewSet):
    queryset = Asset.objects.select_related('category', 'assigned_to__user', 'assigned_to__department').all()
    serializer_class = AssetSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Filter assets based on user role and permissions
        """
        user = self.request.user

        # Get user's role
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        # Get user's employee record
        try:
            employee = user.employee
        except:
            employee = None

        base_queryset = Asset.objects.select_related('category', 'assigned_to__user', 'assigned_to__department').all()

        # Role-based filtering
        if user_role in ['SUPERADMIN', 'ADMIN']:
            # Full access to all assets
            return base_queryset

        elif user_role == 'HR_MANAGER':
            # HR can see all assets for management purposes
            return base_queryset

        elif user_role == 'FINANCE_MANAGER':
            # Finance can see all assets for budgeting/cost tracking
            return base_queryset

        elif user_role == 'DEPARTMENT_MANAGER':
            # Department managers see assets in their department + assigned to them
            if employee and employee.department:
                return base_queryset.filter(
                    models.Q(assigned_to__department=employee.department) |
                    models.Q(assigned_to=employee)
                ).distinct()
            else:
                return base_queryset.filter(assigned_to=employee) if employee else base_queryset.none()

        elif user_role == 'PROJECT_MANAGER':
            # Project managers see assets assigned to them + their department
            if employee and employee.department:
                return base_queryset.filter(
                    models.Q(assigned_to__department=employee.department) |
                    models.Q(assigned_to=employee)
                ).distinct()
            else:
                return base_queryset.filter(assigned_to=employee) if employee else base_queryset.none()

        elif user_role in ['EMPLOYEE', 'INTERN']:
            # Regular employees see only assets assigned to them + shared department assets
            if employee:
                return base_queryset.filter(
                    models.Q(assigned_to=employee) |
                    models.Q(assigned_to__isnull=True, location__icontains=employee.department.name if employee.department else '')
                ).distinct()
            else:
                return base_queryset.none()

        else:
            # No role or unknown role - no access
            return base_queryset.none()

    def perform_create(self, serializer):
        """
        Only allow certain roles to create assets
        """
        user = self.request.user
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        if user_role not in ['SUPERADMIN', 'ADMIN', 'HR_MANAGER']:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to create assets.")

        serializer.save()

    def perform_update(self, serializer):
        """
        Role-based update permissions
        """
        user = self.request.user
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        if user_role not in ['SUPERADMIN', 'ADMIN', 'HR_MANAGER']:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to edit assets.")

        serializer.save()

    def perform_destroy(self, instance):
        """
        Only admins can delete assets
        """
        user = self.request.user
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        if user_role not in ['SUPERADMIN', 'ADMIN']:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to delete assets.")

        instance.delete()

class SupplierViewSet(viewsets.ModelViewSet):
    queryset = Supplier.objects.filter(is_active=True)
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated]

class PurchaseOrderViewSet(viewsets.ModelViewSet):
    queryset = PurchaseOrder.objects.select_related('supplier', 'requested_by__user', 'approved_by__user', 'department', 'project').all()
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated]

# Communication & Collaboration ViewSets
class AnnouncementViewSet(viewsets.ModelViewSet):
    queryset = Announcement.objects.select_related('author__user').prefetch_related('target_departments', 'target_employees').all()
    serializer_class = AnnouncementSerializer
    permission_classes = [IsAuthenticated, EmployeePermission]

    def get_queryset(self):
        """Filter announcements based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            # Try to get user profile and role
            employee = Employee.objects.select_related('user').get(user=user)
            user_profile = getattr(user, 'userprofile', None)
            user_role = getattr(user_profile, 'role', None) if user_profile else None

            # Super admin and admin see all announcements
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see published announcements targeted to them
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(
                    is_published=True
                ).filter(
                    Q(target_departments=employee.department) |
                    Q(target_employees=employee) |
                    Q(target_departments__isnull=True, target_employees__isnull=True)  # General announcements
                )

            # Other roles see all published announcements
            return queryset.filter(is_published=True)
        except Employee.DoesNotExist:
            # If user has no employee record, show only published general announcements
            return queryset.filter(
                is_published=True,
                target_departments__isnull=True,
                target_employees__isnull=True
            )
        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in AnnouncementViewSet.get_queryset: {str(e)}")
            # Return only published announcements as fallback
            return queryset.filter(is_published=True)

class MessageViewSet(viewsets.ModelViewSet):
    queryset = Message.objects.select_related('sender__user', 'recipient__user').all()
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]

class DocumentViewSet(viewsets.ModelViewSet):
    queryset = Document.objects.select_related('uploaded_by__user', 'department').prefetch_related('access_permissions').all()
    serializer_class = DocumentSerializer
    permission_classes = [IsAuthenticated, EmployeePermission]

    def get_queryset(self):
        """Filter documents based on user role and access level"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            user_profile = user.userprofile
            user_role = user_profile.role

            # Super admin and admin see all documents
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see public and internal documents
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(access_level__in=['public', 'internal'])

            # HR managers see HR-related documents
            if user_role and user_role.name == 'HR_MANAGER':
                return queryset.filter(
                    Q(access_level__in=['public', 'internal', 'restricted']) |
                    Q(department__name='Human Resources')
                )

            # Other roles see documents based on department and access level
            return queryset.filter(
                Q(access_level__in=['public', 'internal']) |
                Q(department=user_profile.department)
            )
        except:
            return queryset.filter(access_level='public')

class MeetingViewSet(viewsets.ModelViewSet):
    queryset = Meeting.objects.select_related('organizer__user').prefetch_related('attendees').all()
    serializer_class = MeetingSerializer
    permission_classes = [IsAuthenticated]

class PersonalCalendarViewSet(viewsets.ModelViewSet):
    """Personal Calendar ViewSet - provides calendar events for the current user"""
    serializer_class = PersonalCalendarSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get meetings where user is organizer or attendee"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            return Meeting.objects.select_related('organizer__user').prefetch_related('attendees').filter(
                models.Q(organizer=employee) | models.Q(attendees=employee)
            ).distinct()
        except Employee.DoesNotExist:
            return Meeting.objects.none()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in PersonalCalendarViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return Meeting.objects.none()

class EmployeeLeaveViewSet(viewsets.ModelViewSet):
    """Employee Leave ViewSet - provides leave requests with frontend-compatible data structure"""
    serializer_class = EmployeeLeaveSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['leave_type__name', 'leave_type__name_ar', 'reason', 'reason_ar', 'status']
    filterset_fields = ['status', 'leave_type']
    ordering_fields = ['created_at', 'start_date', 'end_date']
    ordering = ['-created_at']

    def create(self, request, *args, **kwargs):
        """Override create to add debugging"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"EmployeeLeaveViewSet.create - Request data: {request.data}")
        logger.info(f"EmployeeLeaveViewSet.create - User: {request.user.username}")

        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"EmployeeLeaveViewSet.create - Error: {e}")
            raise

    def destroy(self, request, *args, **kwargs):
        """Override destroy to return proper JSON response"""
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response(
                {"success": True, "message": "Leave request deleted successfully"},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"EmployeeLeaveViewSet.destroy - Error: {e}")
            return Response(
                {"success": False, "message": "Failed to delete leave request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_queryset(self):
        """Get leave requests for the current user"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            return LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user').filter(
                employee=employee
            ).order_by('-created_at')
        except Employee.DoesNotExist:
            return LeaveRequest.objects.none()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in EmployeeLeaveViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return LeaveRequest.objects.none()

    @action(detail=False, methods=['get'], url_path='my-requests', permission_classes=[IsAuthenticated])
    def my_requests(self, request):
        """Get current user's leave requests (alias for compatibility)"""
        try:
            employee = Employee.objects.get(user=request.user)
            queryset = LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user').filter(
                employee=employee
            ).order_by('-created_at')
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in EmployeeLeaveViewSet.my_requests for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

# Customer Management ViewSets
class CustomerViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Remove invalid select_related field
    # Customer model doesn't have 'assigned_to' field
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by customer type
        customer_type = self.request.query_params.get('customer_type')
        if customer_type:
            queryset = queryset.filter(customer_type=customer_type)

        # Search by name or email
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search) |
                Q(company_name__icontains=search)
            )

        return queryset.order_by('-created_at')

# Product Management ViewSets
class ProductCategoryViewSet(viewsets.ModelViewSet):
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('name')

class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.select_related('category', 'supplier').all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        # Search by name or SKU
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(sku__icontains=search) |
                Q(brand__icontains=search)
            )

        return queryset.order_by('-created_at')

# Report Management ViewSets
class ReportViewSet(viewsets.ModelViewSet):
    queryset = Report.objects.select_related('created_by').all()
    serializer_class = ReportSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by type
        type_filter = self.request.query_params.get('type')
        if type_filter:
            queryset = queryset.filter(type=type_filter)

        # Search by name or description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(description_ar__icontains=search)
            )

        return queryset.order_by('-created_date')

    def perform_create(self, serializer):
        # Get the employee associated with the current user
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(created_by=employee)
        except Employee.DoesNotExist:
            # If no employee found, create a basic one or handle appropriately
            serializer.save(created_by=None)

# Workflow Management ViewSets
class WorkflowViewSet(viewsets.ModelViewSet):
    queryset = Workflow.objects.select_related('created_by__user').all()
    serializer_class = WorkflowSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by category
        category = self.request.query_params.get('category', None)
        if category:
            queryset = queryset.filter(category=category)

        # Filter by status
        status = self.request.query_params.get('status', None)
        if status:
            queryset = queryset.filter(status=status)

        # Filter by priority
        priority = self.request.query_params.get('priority', None)
        if priority:
            queryset = queryset.filter(priority=priority)

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(description__icontains=search) |
                Q(description_ar__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        # Set created_by to current user's employee profile if available
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(created_by=employee)
        except Employee.DoesNotExist:
            serializer.save(created_by=None)

    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a workflow manually"""
        workflow = self.get_object()

        # Update run count
        workflow.run_count += 1

        # Simulate execution (in real implementation, this would trigger actual workflow)
        import random
        success = random.choice([True, True, True, False])  # 75% success rate

        if success:
            workflow.success_count += 1

        workflow.last_run = timezone.now()
        workflow.save()

        return Response({
            'success': success,
            'message': 'Workflow executed successfully' if success else 'Workflow execution failed',
            'run_count': workflow.run_count,
            'success_rate': workflow.success_rate
        })

# Sales Management ViewSets
class SalesOrderViewSet(viewsets.ModelViewSet):
    queryset = SalesOrder.objects.select_related('customer').all()
    serializer_class = SalesOrderSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by priority
        priority_filter = self.request.query_params.get('priority')
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        # Search by order number or customer name
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(order_number__icontains=search) |
                Q(customer__first_name__icontains=search) |
                Q(customer__last_name__icontains=search) |
                Q(customer__company_name__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        # Auto-generate order number if not provided
        if not serializer.validated_data.get('order_number'):
            import uuid
            order_number = f"SO-{uuid.uuid4().hex[:8].upper()}"
            serializer.save(order_number=order_number)
        else:
            serializer.save()

# KPI Management ViewSets
from django.utils import timezone
from datetime import timedelta, date

class KPICategoryViewSet(viewsets.ModelViewSet):
    queryset = None  # Will be set in get_queryset
    serializer_class = None  # Will be set from serializers
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPICategory
        from .serializers import KPICategorySerializer
        self.queryset = KPICategory.objects.all()
        self.serializer_class = KPICategorySerializer

    def get_queryset(self):
        from .models import KPICategory
        queryset = KPICategory.objects.filter(is_active=True)
        return queryset.order_by('sort_order', 'name')

    @action(detail=True, methods=['get'])
    def kpis(self, request, pk=None):
        """Get all KPIs for a specific category"""
        category = self.get_object()
        kpis = category.kpis.filter(status='ACTIVE')

        # Filter by user role permissions
        user_profile = getattr(request.user, 'userprofile', None)
        if user_profile and user_profile.role:
            kpis = kpis.filter(
                Q(visible_to_roles=user_profile.role) |
                Q(visible_to_roles__isnull=True)
            )

        from .serializers import KPISerializer
        serializer = KPISerializer(kpis, many=True)
        return Response(serializer.data)


class KPIViewSet(viewsets.ModelViewSet):
    queryset = None  # Will be set in get_queryset
    serializer_class = None  # Will be set from serializers
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPI
        from .serializers import KPISerializer
        self.queryset = KPI.objects.all()
        self.serializer_class = KPISerializer

    def get_queryset(self):
        from .models import KPI
        queryset = KPI.objects.select_related('category', 'owner', 'created_by')

        # Filter by user role permissions
        user_profile = getattr(self.request.user, 'userprofile', None)
        if user_profile and user_profile.role:
            queryset = queryset.filter(
                Q(visible_to_roles=user_profile.role) |
                Q(visible_to_roles__isnull=True)
            )

        # Filter by status
        status_filter = self.request.query_params.get('status', 'ACTIVE')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category__name=category)

        return queryset.order_by('category__sort_order', 'name')

    def perform_create(self, serializer):
        # Set the creator
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['get'])
    def values(self, request, pk=None):
        """Get historical values for a KPI"""
        kpi = self.get_object()

        # Date range filtering
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        values = kpi.values.all()

        if start_date:
            values = values.filter(period_start__gte=start_date)
        if end_date:
            values = values.filter(period_end__lte=end_date)

        # Limit results
        limit = int(request.query_params.get('limit', 50))
        values = values[:limit]

        from .serializers import KPIValueSerializer
        serializer = KPIValueSerializer(values, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def trend(self, request, pk=None):
        """Get trend data for a KPI"""
        kpi = self.get_object()

        # Get recent values for trend calculation
        days = int(request.query_params.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)

        values = kpi.values.filter(
            period_start__gte=start_date
        ).order_by('period_start')

        data_points = []
        for value in values:
            data_points.append({
                'date': value.period_start.date(),
                'value': float(value.value),
                'period_start': value.period_start,
                'period_end': value.period_end
            })

        # Calculate trend
        trend_direction = 'stable'
        change_percentage = 0

        if len(data_points) >= 2:
            first_value = data_points[0]['value']
            last_value = data_points[-1]['value']
            change_percentage = ((last_value - first_value) / first_value) * 100

            if change_percentage > 1:
                trend_direction = 'up'
            elif change_percentage < -1:
                trend_direction = 'down'

        trend_data = {
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'data_points': data_points,
            'trend_direction': trend_direction,
            'change_percentage': round(change_percentage, 2)
        }

        from .serializers import KPITrendDataSerializer
        serializer = KPITrendDataSerializer(trend_data)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_value(self, request, pk=None):
        """Add a new value for a KPI"""
        kpi = self.get_object()
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=request.user)

        data = request.data.copy()
        data['kpi'] = kpi.id
        data['recorded_by'] = employee.id

        from .serializers import KPIValueSerializer
        serializer = KPIValueSerializer(data=data)
        if serializer.is_valid():
            serializer.save()

            # Check for threshold breaches and create alerts
            self._check_thresholds(kpi, serializer.instance)

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _check_thresholds(self, kpi, kpi_value):
        """Check if KPI value breaches thresholds and create alerts"""
        value = float(kpi_value.value)

        # Check critical threshold
        if kpi.critical_threshold and (
            (kpi.trend_direction == 'UP' and value < float(kpi.critical_threshold)) or
            (kpi.trend_direction == 'DOWN' and value > float(kpi.critical_threshold))
        ):
            self._create_alert(kpi, kpi_value, 'THRESHOLD_BREACH', 'CRITICAL')

        # Check warning threshold
        elif kpi.warning_threshold and (
            (kpi.trend_direction == 'UP' and value < float(kpi.warning_threshold)) or
            (kpi.trend_direction == 'DOWN' and value > float(kpi.warning_threshold))
        ):
            self._create_alert(kpi, kpi_value, 'THRESHOLD_BREACH', 'HIGH')

    def _create_alert(self, kpi, kpi_value, alert_type, severity):
        """Create a KPI alert"""
        from .models import KPIAlert
        KPIAlert.objects.create(
            kpi=kpi,
            alert_type=alert_type,
            severity=severity,
            title=f"KPI Threshold Breach: {kpi.name}",
            title_ar=f"تجاوز عتبة مؤشر الأداء: {kpi.name_ar}",
            message=f"KPI {kpi.name} has breached the {severity.lower()} threshold with value {kpi_value.value}",
            message_ar=f"مؤشر الأداء {kpi.name_ar} تجاوز العتبة {severity.lower()} بقيمة {kpi_value.value}",
            current_value=kpi_value.value,
            threshold_value=kpi.critical_threshold if severity == 'CRITICAL' else kpi.warning_threshold,
            target_value=kpi.target_value
        )

    @action(detail=True, methods=['post'])
    def calculate_automated(self, request, pk=None):
        """Calculate KPI value using algorithms"""
        kpi = self.get_object()

        if not kpi.is_automated:
            return Response(
                {'error': 'KPI is not configured for automated calculation'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Import algorithm service
        from .kpi_algorithms import kpi_algorithm_service
        calculated_value = kpi_algorithm_service.calculate_automated_kpi(kpi)

        if calculated_value is not None:
            # Save the calculated value
            from django.shortcuts import get_object_or_404
            employee = get_object_or_404(Employee, user=request.user)

            kpi_value = KPIValue.objects.create(
                kpi=kpi,
                value=calculated_value,
                period_start=timezone.now().replace(hour=0, minute=0, second=0, microsecond=0),
                period_end=timezone.now(),
                recorded_by=employee,
                is_estimated=True,
                source_data={'method': 'automated_calculation', 'algorithm': 'kpi_algorithm_service'}
            )

            # Check for anomalies
            anomaly_result = kpi_algorithm_service.detect_anomalies(kpi, calculated_value)

            from .serializers import KPIValueSerializer
            serializer = KPIValueSerializer(kpi_value)

            return Response({
                'kpi_value': serializer.data,
                'calculated_value': float(calculated_value),
                'anomaly_detection': anomaly_result,
                'success': True
            })
        else:
            return Response(
                {'error': 'Failed to calculate automated KPI value'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def predict_values(self, request, pk=None):
        """Predict future KPI values using algorithms"""
        kpi = self.get_object()
        periods = int(request.query_params.get('periods', 6))

        from .kpi_algorithms import kpi_algorithm_service
        predictions = kpi_algorithm_service.predict_future_values(kpi, periods)

        return Response({
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'predictions': predictions,
            'algorithm': 'time_series_analysis'
        })

    @action(detail=True, methods=['get'])
    def analyze_trend(self, request, pk=None):
        """Analyze KPI trend using algorithms"""
        kpi = self.get_object()
        periods = int(request.query_params.get('periods', 12))

        from .kpi_algorithms import kpi_algorithm_service
        trend_analysis = kpi_algorithm_service.analyze_trend(kpi, periods)

        return Response({
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'trend_analysis': trend_analysis
        })

    @action(detail=True, methods=['get'])
    def get_recommendations(self, request, pk=None):
        """Get AI-powered recommendations for KPI improvement"""
        kpi = self.get_object()

        from .kpi_algorithms import kpi_algorithm_service
        recommendations = kpi_algorithm_service.generate_recommendations(kpi)

        return Response({
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'recommendations': recommendations,
            'generated_at': timezone.now()
        })

    @action(detail=True, methods=['post'])
    def detect_anomaly(self, request, pk=None):
        """Detect anomalies in KPI value"""
        kpi = self.get_object()
        value = request.data.get('value')

        if value is None:
            return Response(
                {'error': 'Value is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from decimal import Decimal
            value_decimal = Decimal(str(value))

            from .kpi_algorithms import kpi_algorithm_service
            anomaly_result = kpi_algorithm_service.detect_anomalies(kpi, value_decimal)

            return Response({
                'kpi_id': kpi.id,
                'value': float(value_decimal),
                'anomaly_detection': anomaly_result
            })
        except (ValueError, TypeError):
            return Response(
                {'error': 'Invalid value format'},
                status=status.HTTP_400_BAD_REQUEST
            )


class KPIValueViewSet(viewsets.ModelViewSet):
    queryset = None
    serializer_class = None
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPIValue
        from .serializers import KPIValueSerializer
        self.queryset = KPIValue.objects.all()
        self.serializer_class = KPIValueSerializer

    def get_queryset(self):
        from .models import KPIValue
        queryset = KPIValue.objects.select_related(
            'kpi', 'recorded_by', 'department', 'project', 'employee'
        )

        # Filter by KPI
        kpi_id = self.request.query_params.get('kpi')
        if kpi_id:
            queryset = queryset.filter(kpi_id=kpi_id)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(period_start__gte=start_date)
        if end_date:
            queryset = queryset.filter(period_end__lte=end_date)

        return queryset.order_by('-period_start')

    def perform_create(self, serializer):
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=self.request.user)
        serializer.save(recorded_by=employee)


class KPITargetViewSet(viewsets.ModelViewSet):
    queryset = None
    serializer_class = None
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPITarget
        from .serializers import KPITargetSerializer
        self.queryset = KPITarget.objects.all()
        self.serializer_class = KPITargetSerializer

    def get_queryset(self):
        from .models import KPITarget
        queryset = KPITarget.objects.select_related(
            'kpi', 'created_by', 'department', 'project', 'employee'
        )

        # Filter by KPI
        kpi_id = self.request.query_params.get('kpi')
        if kpi_id:
            queryset = queryset.filter(kpi_id=kpi_id)

        # Filter by active targets
        active_only = self.request.query_params.get('active_only', 'false').lower() == 'true'
        if active_only:
            today = date.today()
            queryset = queryset.filter(start_date__lte=today, end_date__gte=today)

        return queryset.order_by('-start_date')

    def perform_create(self, serializer):
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=self.request.user)
        serializer.save(created_by=employee)


class KPIAlertViewSet(viewsets.ModelViewSet):
    queryset = None
    serializer_class = None
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPIAlert
        from .serializers import KPIAlertSerializer
        self.queryset = KPIAlert.objects.all()
        self.serializer_class = KPIAlertSerializer

    def get_queryset(self):
        from .models import KPIAlert
        queryset = KPIAlert.objects.select_related(
            'kpi', 'acknowledged_by', 'resolved_by'
        )

        # Filter by status
        status_filter = self.request.query_params.get('status', 'ACTIVE')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by severity
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        return queryset.order_by('-created_at')

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge an alert"""
        alert = self.get_object()
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=request.user)

        alert.status = 'ACKNOWLEDGED'
        alert.acknowledged_by = employee
        alert.acknowledged_at = timezone.now()
        alert.save()

        serializer = self.get_serializer(alert)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve an alert"""
        alert = self.get_object()
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=request.user)

        alert.status = 'RESOLVED'
        alert.resolved_by = employee
        alert.resolved_at = timezone.now()
        alert.resolution_notes = request.data.get('resolution_notes', '')
        alert.save()

        serializer = self.get_serializer(alert)
        return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def kpi_dashboard(request):
    """
    Get comprehensive KPI dashboard data
    """
    try:
        from .models import KPI, KPICategory, KPIAlert
        from .serializers import KPICategorySerializer, KPIAlertSerializer, KPISerializer
        from django.db.models import Count

        user_profile = getattr(request.user, 'userprofile', None)
        user_role = user_profile.role if user_profile else None

        # CRITICAL FIX: Get accessible KPIs based on role with optimized queries
        kpis = KPI.objects.select_related('category', 'owner__user').prefetch_related('values', 'targets').filter(status='ACTIVE')
        if user_role:
            kpis = kpis.filter(
                Q(visible_to_roles=user_role) |
                Q(visible_to_roles__isnull=True)
            )

        # CRITICAL FIX: Categories with KPI counts - optimized
        categories = KPICategory.objects.filter(is_active=True).annotate(
            kpi_count=Count('kpis', filter=Q(kpis__status='ACTIVE'))
        ).order_by('sort_order')

        # CRITICAL FIX: Recent alerts with optimized queries
        recent_alerts = KPIAlert.objects.select_related('kpi__category', 'triggered_by__user').filter(
            status__in=['ACTIVE', 'ACKNOWLEDGED']
        ).order_by('-created_at')[:10]

        # KPI performance analysis
        kpis_with_values = []
        for kpi in kpis:
            latest_value = kpi.values.first()
            if latest_value and kpi.target_value:
                achievement = (float(latest_value.value) / float(kpi.target_value)) * 100
                kpis_with_values.append({
                    'kpi': kpi,
                    'achievement': achievement,
                    'latest_value': latest_value
                })

        # Sort by achievement
        kpis_with_values.sort(key=lambda x: x['achievement'], reverse=True)

        top_performing = [item['kpi'] for item in kpis_with_values[:5]]
        underperforming = [item['kpi'] for item in kpis_with_values[-5:]]

        # Summary statistics
        total_kpis = kpis.count()
        kpis_on_target = len([item for item in kpis_with_values if 95 <= item['achievement'] <= 105])
        kpis_above_target = len([item for item in kpis_with_values if item['achievement'] > 105])
        kpis_below_target = len([item for item in kpis_with_values if item['achievement'] < 95])

        active_alerts = KPIAlert.objects.filter(status='ACTIVE').count()
        critical_alerts = KPIAlert.objects.filter(status='ACTIVE', severity='CRITICAL').count()

        kpi_summary = {
            'total_kpis': total_kpis,
            'active_kpis': total_kpis,
            'kpis_on_target': kpis_on_target,
            'kpis_above_target': kpis_above_target,
            'kpis_below_target': kpis_below_target,
            'active_alerts': active_alerts,
            'critical_alerts': critical_alerts,
            'categories_count': categories.count(),
            'last_updated': timezone.now()
        }

        # Serialize data
        dashboard_data = {
            'categories': KPICategorySerializer(categories, many=True).data,
            'recent_alerts': KPIAlertSerializer(recent_alerts, many=True).data,
            'top_performing_kpis': KPISerializer(top_performing, many=True).data,
            'underperforming_kpis': KPISerializer(underperforming, many=True).data,
            'kpi_summary': kpi_summary
        }

        return Response(dashboard_data)

    except Exception as e:
        return Response(
            {'error': f'Failed to load KPI dashboard: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Import new models
from .models import PerformanceReview, PayrollPeriod, PayrollEntry, JobPosting, TrainingProgram, Invoice, CostCenter

# New ViewSets for missing endpoints

# Performance Review ViewSet
class PerformanceReviewViewSet(viewsets.ModelViewSet):
    queryset = PerformanceReview.objects.all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['employee', 'reviewer', 'review_type', 'is_final']
    search_fields = ['employee__user__first_name', 'employee__user__last_name']
    ordering_fields = ['review_period_start', 'review_period_end', 'created_at']
    ordering = ['-review_period_end']


# Payroll ViewSets
class PayrollViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    queryset = PayrollEntry.objects.select_related('employee__user', 'payroll_period').all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['employee', 'payroll_period', 'is_paid']
    search_fields = ['employee__user__first_name', 'employee__user__last_name']
    ordering_fields = ['created_at', 'net_salary']
    ordering = ['-created_at']


# Recruitment ViewSets
class RecruitmentViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    queryset = JobPosting.objects.select_related('department', 'hiring_manager__user').all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'status', 'employment_type']
    search_fields = ['title', 'title_ar', 'description']
    ordering_fields = ['posted_date', 'closing_date', 'created_at']
    ordering = ['-posted_date']


# Training ViewSet
class TrainingViewSet(viewsets.ModelViewSet):
    queryset = TrainingProgram.objects.all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'status', 'is_mandatory']
    search_fields = ['title', 'title_ar', 'description', 'instructor']
    ordering_fields = ['start_date', 'end_date', 'created_at']
    ordering = ['-start_date']


# Invoice ViewSet
class InvoiceViewSet(viewsets.ModelViewSet):
    queryset = Invoice.objects.all()
    serializer_class = ExpenseSerializer  # Using Expense serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['customer', 'status']
    search_fields = ['invoice_number', 'customer__first_name', 'customer__last_name']
    ordering_fields = ['issue_date', 'due_date', 'total_amount', 'created_at']
    ordering = ['-created_at']


# Cost Center ViewSet
class CostCenterViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    queryset = CostCenter.objects.select_related('department', 'manager__user').all()
    serializer_class = DepartmentSerializer  # Using Department serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'manager', 'is_active']
    search_fields = ['code', 'name', 'name_ar']
    ordering_fields = ['code', 'name', 'budget_allocated', 'created_at']
    ordering = ['code']


# Security monitoring helper functions
def calculate_security_score():
    """Calculate real-time security score based on system metrics"""
    try:
        score = 100

        # Check for recent failed logins (reduce score if too many)
        from django.contrib.admin.models import LogEntry
        from datetime import datetime, timedelta

        recent_failures = LogEntry.objects.filter(
            action_time__gte=datetime.now() - timedelta(hours=24),
            action_flag=3  # DELETION flag often used for failed attempts
        ).count()

        if recent_failures > 10:
            score -= min(30, recent_failures * 2)

        # Check for inactive users (security risk)
        inactive_users = User.objects.filter(is_active=False).count()
        total_users = User.objects.count()

        if total_users > 0:
            inactive_ratio = inactive_users / total_users
            if inactive_ratio > 0.2:  # More than 20% inactive
                score -= 10

        # Check for users without proper roles
        users_without_profiles = User.objects.filter(userprofile__isnull=True).count()
        if users_without_profiles > 0:
            score -= min(20, users_without_profiles * 5)

        return max(0, min(100, score))

    except Exception:
        return 0


def get_active_threats_count():
    """Get count of active security threats"""
    try:
        # In a real implementation, this would connect to security monitoring
        # For now, return 0 as we don't have active threat detection
        return 0
    except Exception:
        return 0


def get_blocked_attacks_count():
    """Get count of blocked attacks in the last 24 hours"""
    try:
        # In a real implementation, this would read from security logs
        # For now, calculate based on failed login attempts as a proxy
        from django.contrib.admin.models import LogEntry
        from datetime import datetime, timedelta

        blocked_count = LogEntry.objects.filter(
            action_time__gte=datetime.now() - timedelta(hours=24),
            action_flag=3  # Failed attempts
        ).count()

        return blocked_count

    except Exception:
        return 0


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def financial_analytics(request):
    """
    Get financial analytics and trends
    """
    try:
        from datetime import datetime, timedelta
        from django.db.models import Sum, Avg, Count

        # Calculate date ranges
        now = datetime.now()
        current_month = now.replace(day=1)
        last_month = (current_month - timedelta(days=1)).replace(day=1)
        current_year = now.replace(month=1, day=1)
        last_year = current_year.replace(year=current_year.year - 1)

        # Current month expenses
        current_month_expenses = Expense.objects.filter(
            expense_date__gte=current_month,
            status='APPROVED'
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Last month expenses
        last_month_expenses = Expense.objects.filter(
            expense_date__gte=last_month,
            expense_date__lt=current_month,
            status='APPROVED'
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Calculate growth rate
        expense_growth_rate = 0
        if last_month_expenses > 0:
            expense_growth_rate = ((current_month_expenses - last_month_expenses) / last_month_expenses) * 100

        # Budget utilization
        total_budgets = Budget.objects.filter(is_active=True).aggregate(total=Sum('allocated_amount'))['total'] or 0
        budget_utilization = 0
        if total_budgets > 0:
            budget_utilization = (current_month_expenses / total_budgets) * 100

        # Department-wise expenses
        dept_expenses = Expense.objects.filter(
            expense_date__gte=current_month,
            status='APPROVED'
        ).values('category').annotate(
            total=Sum('amount'),
            count=Count('id')
        ).order_by('-total')[:5]

        data = {
            'current_month_expenses': float(current_month_expenses),
            'last_month_expenses': float(last_month_expenses),
            'expense_growth_rate': round(expense_growth_rate, 2),
            'budget_utilization': round(budget_utilization, 2),
            'total_budgets': float(total_budgets),
            'department_expenses': list(dept_expenses),
            'generated_at': now.isoformat()
        }

        return Response(data)

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in financial_analytics: {str(e)}")
        return Response(
            {'error': 'Failed to fetch financial analytics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# FIXED: Employee Dashboard API endpoints
@api_view(['GET'])
@permission_classes([AllowAny])
def my_meetings(request):
    """
    Get current user's upcoming meetings
    """
    try:
        # For now, return empty array since we don't have meeting data
        # This should be replaced with real meeting data when available
        meetings = []

        return Response(meetings)

    except Exception as e:
        return Response(
            {'error': f'Failed to get meetings: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def recent_messages(request):
    """
    Get current user's recent messages
    """
    try:
        # For now, return empty array since we don't have message data
        # This should be replaced with real message data when available
        messages = []

        return Response(messages)

    except Exception as e:
        return Response(
            {'error': f'Failed to get messages: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class EmployeeTaskViewSet(viewsets.ModelViewSet):
    """
    Employee Tasks ViewSet - provides tasks filtered for the current employee
    This is the missing /api/employee-tasks/ endpoint that the frontend expects
    """
    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description', 'project__name', 'status']
    filterset_fields = ['status', 'priority', 'project']
    ordering_fields = ['created_at', 'due_date', 'priority', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter tasks for the current employee only"""
        try:
            # Get the employee record for the current user
            employee = Employee.objects.get(user=self.request.user)
            # Return tasks assigned to this employee
            return Task.objects.select_related(
                'project', 'assigned_to__user', 'created_by__user'
            ).filter(assigned_to=employee)
        except Employee.DoesNotExist:
            # If no employee record found, return empty queryset
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"No employee record found for user {self.request.user.username}")
            return Task.objects.none()
        except Exception as e:
            # Handle any other errors gracefully
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in EmployeeTaskViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return Task.objects.none()

    def perform_create(self, serializer):
        """Set the created_by field when creating a task"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(created_by=employee)
        except Employee.DoesNotExist:
            # If no employee found, save without created_by
            serializer.save(created_by=None)

class PersonalMessageViewSet(viewsets.ModelViewSet):
    """
    Personal Messages ViewSet - provides messages filtered for the current user
    This is the missing /api/personal-messages/ endpoint that the frontend expects
    """
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['subject', 'content', 'sender__user__first_name', 'sender__user__last_name']
    filterset_fields = ['is_read', 'is_important']  # FIXED: Use actual Message model fields
    ordering_fields = ['sent_at', 'is_read', 'is_important']  # FIXED: Use actual Message model fields
    ordering = ['-sent_at']  # FIXED: Use actual Message model field

    def get_queryset(self):
        """Filter messages for the current user (sent or received)"""
        try:
            # Get the employee record for the current user
            employee = Employee.objects.get(user=self.request.user)
            # Return messages where user is sender or recipient
            from django.db.models import Q
            return Message.objects.select_related(
                'sender__user', 'recipient__user'
            ).filter(
                Q(sender=employee) | Q(recipient=employee)
            )
        except Employee.DoesNotExist:
            # If no employee record found, return empty queryset
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"No employee record found for user {self.request.user.username}")
            return Message.objects.none()
        except Exception as e:
            # Handle any other errors gracefully
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in PersonalMessageViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return Message.objects.none()

    def perform_create(self, serializer):
        """Set the sender field when creating a message"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(sender=employee)
        except Employee.DoesNotExist:
            # If no employee found, raise a proper error
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"No employee record found for user {self.request.user.username} when creating message")
            from rest_framework.exceptions import ValidationError
            raise ValidationError("Employee record not found. Cannot create message.")

@api_view(['GET'])
@permission_classes([AllowAny])
def my_tasks_dashboard(request):
    """
    Get current user's tasks for dashboard
    """
    try:
        user = request.user

        # Get tasks assigned to current user from Task model
        try:
            tasks = Task.objects.filter(assigned_to=user).order_by('-created_at')[:10]

            task_data = []
            for task in tasks:
                task_data.append({
                    'id': task.id,
                    'title': task.title,
                    'description': task.description,
                    'status': task.status,
                    'priority': task.priority,
                    'due_date': task.due_date,
                    'created_at': task.created_at,
                    'project': task.project.name if hasattr(task, 'project') and task.project else None,
                })

            return Response(task_data)
        except Exception:
            # If Task model doesn't exist or has issues, return empty array
            return Response([])

    except Exception as e:
        return Response(
            {'error': f'Failed to get tasks: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
